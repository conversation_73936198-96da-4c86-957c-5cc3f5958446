/**
 * CAPTCHA verification utilities for FaceTrace security
 */

import axios from 'axios';

export interface CaptchaVerificationResult {
  success: boolean;
  error?: string;
  score?: number; // For reCAPTCHA v3
  action?: string; // For reCAPTCHA v3
}

/**
 * Verify reCAPTCHA Enterprise token on the server side
 * @param token - The reCAPTCHA token from the client
 * @param remoteip - Optional IP address of the user
 * @param expectedAction - Expected action for the token (e.g., 'FACETRACE_SEARCH')
 * @returns Promise<CaptchaVerificationResult>
 */
export async function verifyCaptcha(
  token: string,
  remoteip?: string,
  expectedAction: string = 'FACETRACE_SEARCH'
): Promise<CaptchaVerificationResult> {
  const secretKey = process.env.RECAPTCHA_SECRET_KEY;

  if (!secretKey) {
    console.error('[captcha] RECAPTCHA_SECRET_KEY not configured');
    return {
      success: false,
      error: 'CAPTCHA verification not configured'
    };
  }

  if (!token) {
    return {
      success: false,
      error: 'CAPTCHA token is required'
    };
  }

  console.log(`[captcha] Verifying token for action: ${expectedAction}, IP: ${remoteip || 'unknown'}`);

  // For development/testing, use a more lenient approach
  const isDevelopment = process.env.NODE_ENV === 'development' ||
                       process.env.NODE_ENV === 'test';

  // Development bypass option - completely skip CAPTCHA verification if enabled
  const bypassInDev = process.env.BYPASS_CAPTCHA_IN_DEV === 'true';
  if (isDevelopment && bypassInDev) {
    console.warn('[captcha] Development mode: Bypassing CAPTCHA verification entirely');
    return {
      success: true,
      score: 0.9,
      action: expectedAction
    };
  }

  // Check if we have Enterprise configuration
  const projectId = process.env.GOOGLE_CLOUD_PROJECT_ID;
  const siteKey = process.env.NEXT_PUBLIC_RECAPTCHA_SITE_KEY;
  const apiKey = process.env.GOOGLE_CLOUD_API_KEY || secretKey; // Use dedicated API key or fallback to secret key
  const forceStandard = process.env.FORCE_STANDARD_RECAPTCHA === 'true';
  const useEnterprise = !forceStandard && projectId && siteKey && apiKey && !isDevelopment;

  // Enterprise API endpoint
  const enterpriseUrl = `https://recaptchaenterprise.googleapis.com/v1/projects/${projectId}/assessments`;
  // Standard API endpoint (fallback)
  const standardUrl = 'https://www.google.com/recaptcha/api/siteverify';

  let response;
  let data;

  // Try Enterprise API first if configured
  if (useEnterprise && !isDevelopment) {
    try {
      console.log('[captcha] Using reCAPTCHA Enterprise API...');

      const enterprisePayload = {
        event: {
          token: token,
          expectedAction: expectedAction,
          siteKey: siteKey
        }
      };

      // Use proper API key authentication for Enterprise API
      const enterpriseUrlWithKey = `${enterpriseUrl}?key=${apiKey}`;

      response = await axios.post(enterpriseUrlWithKey, enterprisePayload, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        timeout: 10000,
      });

      data = response.data;

      // Enterprise API response structure
      if (data.tokenProperties) {
        const tokenProps = data.tokenProperties;
        const riskAnalysis = data.riskAnalysis || {};

        console.log('[captcha] Enterprise verification response:', {
          valid: tokenProps.valid,
          action: tokenProps.action,
          score: riskAnalysis.score,
          hostname: tokenProps.hostname,
          reasons: riskAnalysis.reasons
        });

        if (!tokenProps.valid) {
          console.warn('[captcha] Enterprise token validation failed:', tokenProps);
          return {
            success: false,
            error: 'CAPTCHA verification failed: invalid token'
          };
        }

        // Check if action matches expected action (case-insensitive)
        if (tokenProps.action && tokenProps.action.toLowerCase() !== expectedAction.toLowerCase()) {
          console.warn(`[captcha] Action mismatch: expected ${expectedAction}, got ${tokenProps.action}`);
          return {
            success: false,
            error: 'CAPTCHA verification failed: action mismatch'
          };
        }

        // Check risk score (0.0 to 1.0, higher is better)
        const score = riskAnalysis.score !== undefined ? riskAnalysis.score : 0;
        const minScore = 0.5; // Configurable threshold

        console.log(`[captcha] Enterprise risk score: ${score} (threshold: ${minScore})`);

        if (score < minScore) {
          console.warn(`[captcha] Risk score too low: ${score} < ${minScore}`);
          if (riskAnalysis.reasons) {
            console.warn(`[captcha] Risk reasons:`, riskAnalysis.reasons);
          }
          return {
            success: false,
            error: 'CAPTCHA verification failed: suspicious activity detected'
          };
        }

        return {
          success: true,
          score: score,
          action: tokenProps.action
        };
      } else {
        console.warn('[captcha] No tokenProperties in Enterprise response:', data);
        return {
          success: false,
          error: 'CAPTCHA verification failed: invalid response format'
        };
      }
    } catch (enterpriseError) {
      console.warn('[captcha] Enterprise API failed, falling back to standard API:', enterpriseError);
    }
  }

  // Fallback to standard reCAPTCHA API
  try {
    console.log(`[captcha] Using standard reCAPTCHA API (development: ${isDevelopment})...`);

    const params = new URLSearchParams({
      secret: secretKey,
      response: token,
      ...(remoteip && { remoteip })
    });

    response = await axios.post(standardUrl, params, {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      timeout: 10000,
    });

    data = response.data;

    console.log('[captcha] Standard verification response:', {
      success: data.success,
      score: data.score,
      action: data.action,
      hostname: data.hostname,
      challenge_ts: data.challenge_ts
    });

    if (!data.success) {
      const errorCodes = data['error-codes'] || [];
      console.warn('[captcha] Standard verification failed:', errorCodes);

      // In development, be more lenient with certain errors
      if (isDevelopment && (
        errorCodes.includes('invalid-input-secret') ||
        errorCodes.includes('invalid-input-response') ||
        errorCodes.includes('browser-error') ||
        errorCodes.includes('hostname-mismatch')
      )) {
        console.warn('[captcha] Development mode: Allowing request despite CAPTCHA errors:', errorCodes);
        return {
          success: true,
          score: 0.9, // High score for development
          action: expectedAction
        };
      }

      return {
        success: false,
        error: `CAPTCHA verification failed: ${errorCodes.join(', ')}`
      };
    }

    // For reCAPTCHA v3, check the score (0.0 to 1.0, higher is better)
    if (data.score !== undefined) {
      const minScore = isDevelopment ? 0.1 : 0.5; // More lenient in development
      if (data.score < minScore) {
        console.warn(`[captcha] Score too low: ${data.score} < ${minScore}`);
        return {
          success: false,
          error: 'CAPTCHA verification failed: suspicious activity detected'
        };
      }
    }

    return {
      success: true,
      score: data.score,
      action: data.action
    };

  } catch (error) {
    console.error('[captcha] Error verifying CAPTCHA:', error);

    if (axios.isAxiosError(error)) {
      if (error.code === 'ECONNABORTED') {
        return {
          success: false,
          error: 'CAPTCHA verification timeout'
        };
      }

      return {
        success: false,
        error: `CAPTCHA verification failed: ${error.message}`
      };
    }

    return {
      success: false,
      error: 'CAPTCHA verification failed: unknown error'
    };
  }
}

/**
 * Extract client IP address from request headers
 * @param request - Next.js request object
 * @returns string | undefined
 */
export function getClientIP(request: Request): string | undefined {
  // Check various headers for the real IP
  const headers = request.headers;
  
  const forwardedFor = headers.get('x-forwarded-for');
  if (forwardedFor) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwardedFor.split(',')[0].trim();
  }
  
  const realIP = headers.get('x-real-ip');
  if (realIP) {
    return realIP;
  }
  
  const cfConnectingIP = headers.get('cf-connecting-ip');
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  // Fallback to other headers
  return headers.get('x-client-ip') || 
         headers.get('x-cluster-client-ip') || 
         headers.get('x-forwarded') || 
         headers.get('forwarded-for') || 
         headers.get('forwarded') || 
         undefined;
}

/**
 * Rate limiting store (in-memory for simplicity)
 * In production, consider using Redis or a database
 */
interface RateLimitEntry {
  count: number;
  resetTime: number;
}

const rateLimitStore = new Map<string, RateLimitEntry>();

/**
 * Simple rate limiting implementation
 * @param identifier - Unique identifier (IP address, user ID, etc.)
 * @param maxRequests - Maximum requests allowed
 * @param windowMs - Time window in milliseconds
 * @returns boolean - true if request is allowed, false if rate limited
 */
export function checkRateLimit(
  identifier: string,
  maxRequests: number = 5,
  windowMs: number = 15 * 60 * 1000 // 15 minutes
): boolean {
  const now = Date.now();
  const entry = rateLimitStore.get(identifier);
  
  if (!entry || now > entry.resetTime) {
    // First request or window expired
    rateLimitStore.set(identifier, {
      count: 1,
      resetTime: now + windowMs
    });
    return true;
  }
  
  if (entry.count >= maxRequests) {
    console.warn(`[rate-limit] Rate limit exceeded for ${identifier}: ${entry.count}/${maxRequests}`);
    return false;
  }
  
  // Increment counter
  entry.count++;
  rateLimitStore.set(identifier, entry);
  
  return true;
}

/**
 * Clean up expired rate limit entries
 * Call this periodically to prevent memory leaks
 */
export function cleanupRateLimit(): void {
  const now = Date.now();
  for (const [key, entry] of rateLimitStore.entries()) {
    if (now > entry.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

// Clean up every 30 minutes
if (typeof setInterval !== 'undefined') {
  setInterval(cleanupRateLimit, 30 * 60 * 1000);
}
